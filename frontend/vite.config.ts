import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import cesium from 'vite-plugin-cesium'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())

  // 调试环境变量
  console.log('Vite配置中的环境变量:', {
    VITE_API_BASE_URL: env.VITE_API_BASE_URL,
    VITE_BACKEND_URL: env.VITE_BACKEND_URL,
    VITE_WEBSOCKET_URL: env.VITE_WEBSOCKET_URL,
    VITE_WEBSOCKET_PATH: env.VITE_WEBSOCKET_PATH,
    NODE_ENV: process.env.NODE_ENV,
    mode: mode
  })

  return {
  plugins: [react(), cesium(), tailwindcss()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    assetsInlineLimit: 0,
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    // 移除代理配置，由 Caddy 处理
  },
  define: {
    CESIUM_BASE_URL: JSON.stringify('/'),
  },
  }
})
