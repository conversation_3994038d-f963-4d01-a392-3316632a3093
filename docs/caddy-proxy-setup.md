# Caddy 反向代理配置说明

## 概述

本项目现在使用 Caddy 作为反向代理服务器，将前端 Vite 开发服务器和后端 FastAPI 服务统一对外提供服务。

## 架构图

```
用户请求 → Caddy (80/443) → 前端 Vite (5173) 或 后端 FastAPI (8000)
```

## 服务配置

### 1. Caddy 服务
- **端口**: 80 (HTTP), 443 (HTTPS)
- **功能**: 反向代理、SSL 终止、静态文件缓存
- **配置文件**: `caddy/Caddyfile`

### 2. 前端服务 (Vite)
- **端口**: 5173
- **功能**: React 开发服务器、热重载 (HMR)
- **配置文件**: `frontend/vite.config.ts`

### 3. 后端服务 (FastAPI)
- **端口**: 8000
- **功能**: API 服务、WebSocket 服务
- **配置文件**: `backend/fastapi_app/main.py`

## 路由规则

### Caddy 路由配置

1. **API 请求**: `/api/*` → `backend:8000`
2. **WebSocket**: `/ws/*` → `backend:8000`
3. **前端页面**: `/*` → `frontend:5173`

### 前端环境变量

```env
VITE_API_BASE_URL=/api/v1
VITE_BACKEND_URL=/api
VITE_WEBSOCKET_URL=/ws
VITE_WEBSOCKET_PATH=/ws
```

## 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 测试配置

运行测试脚本验证配置：

```bash
./test-proxy.sh
```

## 访问地址

- **生产环境**: https://buoy.hanjie-tech.cn
- **开发环境**: http://localhost

## 优势

1. **统一入口**: 所有请求通过 Caddy 统一处理
2. **自动 HTTPS**: Caddy 自动申请和续期 SSL 证书
3. **开发友好**: 保留 Vite 热重载功能
4. **性能优化**: Caddy 提供 gzip 压缩和缓存
5. **安全性**: 统一的安全头设置

## 注意事项

1. 前端不再需要配置代理，所有代理由 Caddy 处理
2. WebSocket 连接通过 Caddy 转发，支持 Vite HMR
3. 开发环境和生产环境使用相同的代理配置
4. 确保防火墙开放 80 和 443 端口

## 故障排除

### 常见问题

1. **前端页面无法加载**
   - 检查 frontend 服务是否正常运行
   - 确认 Vite 服务监听在 0.0.0.0:5173

2. **API 请求失败**
   - 检查 backend 服务是否正常运行
   - 确认后端服务监听在 0.0.0.0:8000

3. **WebSocket 连接失败**
   - 检查 Caddy 配置中的 WebSocket 转发设置
   - 确认后端 WebSocket 端点正常工作

### 调试命令

```bash
# 查看特定服务日志
docker-compose logs frontend
docker-compose logs backend
docker-compose logs caddy

# 进入容器调试
docker-compose exec frontend sh
docker-compose exec backend bash
docker-compose exec caddy sh

# 测试内部网络连通性
docker-compose exec caddy wget -qO- http://frontend:5173
docker-compose exec caddy wget -qO- http://backend:8000/api/v1/health
```
