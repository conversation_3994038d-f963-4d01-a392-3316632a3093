#!/bin/bash

# 测试 Caddy 反向代理配置的脚本

echo "=== 浮标项目 Caddy 反向代理测试 ==="
echo ""

# 检查服务状态
echo "1. 检查 Docker 服务状态..."
docker-compose ps

echo ""
echo "2. 检查端口占用情况..."
echo "Caddy (80, 443):"
netstat -tlnp | grep -E ':80|:443' || echo "端口未被占用"

echo ""
echo "Frontend (5173):"
netstat -tlnp | grep ':5173' || echo "端口未被占用"

echo ""
echo "Backend (8000):"
netstat -tlnp | grep ':8000' || echo "端口未被占用"

echo ""
echo "3. 测试服务连通性..."

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 测试前端服务
echo "测试前端服务 (localhost:5173)..."
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:5173/ || echo "前端服务不可达"

# 测试后端服务
echo "测试后端服务 (localhost:8000)..."
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:8000/api/v1/health || echo "后端服务不可达"

# 测试 Caddy 代理
echo "测试 Caddy 代理 (localhost:80)..."
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost/ || echo "Caddy 代理不可达"

echo ""
echo "4. 查看服务日志..."
echo "Frontend 日志 (最后10行):"
docker-compose logs --tail=10 frontend

echo ""
echo "Backend 日志 (最后10行):"
docker-compose logs --tail=10 backend

echo ""
echo "Caddy 日志 (最后10行):"
docker-compose logs --tail=10 caddy

echo ""
echo "=== 测试完成 ==="
