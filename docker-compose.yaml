name: buoy-project

services:
  # Caddy 反向代理服务
  caddy:
    image: caddy:2-alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./caddy/Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - caddy_logs:/var/log/caddy
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"  # Vite 开发服务器端口
    volumes:
      - ./frontend:/app:z
      - /app/node_modules
    env_file:
      - .env
      - ./frontend/.env
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - VITE_HOST=0.0.0.0  # 允许外部访问
    depends_on:
      - backend
      
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    # 移除端口映射，仅通过 Caddy 访问
    volumes:
      - ./backend/images:/app/images:z
      - ./backend/certs:/app/certs:z
      - backend_logs:/app/logs
    env_file:
      - .env
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENV=${ENV:-production}
    depends_on:
      - db
      - mqtt
    restart: unless-stopped
    # 使用生产模式启动，移除 --reload
    command: uvicorn fastapi_app.main:app --host 0.0.0.0 --port 8000

  db:
    image: postgis/postgis:15-3.3
    # 移除端口映射，仅内部访问
    volumes:
      - postgres_data:/var/lib/postgresql/data
    env_file:
      - .env
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    restart: unless-stopped

  mqtt:
    image: eclipse-mosquitto:2
    user: "1000:1000"
    # 保留 MQTTS 端口用于浮标连接，移除其他端口
    ports:
      - "8883:8883"  # MQTTS 端口
    volumes:
      - ./mosquitto/config:/mosquitto/config:Z
      - ./mosquitto/data:/mosquitto/data:Z
      - ./mosquitto/log:/mosquitto/log:Z
      - ./mosquitto/certs:/mosquitto/certs:Z
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    restart: unless-stopped

  # simulator 服务移至开发环境配置
  # 生产环境使用真实浮标设备

volumes:
  # Caddy 数据持久化
  caddy_data:
  caddy_config:
  caddy_logs:

  # 数据库数据
  postgres_data:

  # 后端数据
  backend_logs:
